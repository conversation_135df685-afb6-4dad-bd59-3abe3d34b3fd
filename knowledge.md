# Foquz Core - Survey Platform

## Project Overview
Foquz is a comprehensive survey and polling platform built with Yii2 PHP framework and a modern frontend using Knockout.js, TypeScript, and Webpack.

## Technology Stack
- **Backend**: Yii2 PHP Framework (PHP 8.2+)
- **Frontend**: Knockout.js with TypeScript
- **Build System**: Webpack 5
- **CSS**: LESS/SASS
- **Database**: MySQL with Redis for caching
- **Queue System**: AMQP/RabbitMQ
- **Testing**: Codeception, Jest, Playwright

## Architecture
- **MVC Pattern**: Standard Yii2 MVC structure
- **Frontend Components**: Located in `ko/` directory with TypeScript models and Knockout.js templates
- **Question Types**: 24+ different question types including rating, matrix, NPS, card sorting, etc.
- **Multi-language Support**: Built-in internationalization
- **Widget System**: Embeddable survey widgets
- **API**: RESTful API with OAuth2 authentication

## Development Workflow
- **Frontend Build**: `npm run watch` for development, `npm run build` for production
- **Linting**: `npm run lint` for ESLint
- **Testing**: `npm test` for Jest, `vendor/bin/codecept run` for PHP tests

## Key Directories
- `ko/` - Frontend TypeScript/Knockout.js code
- `controllers/` - Yii2 controllers
- `models/` - Yii2 models
- `views/` - Yii2 views
- `web/` - Public web assets
- `migrations/` - Database migrations
- `tests/` - Test suites

## Question Types System
The platform supports 24+ question types defined in `ko/data/question-types.js`:
- Basic: Text, Date, File, Address
- Rating: Stars, NPS, Smile, Scale
- Selection: Variants, Matrix, Classifier
- Advanced: Card Sorting, Distribution Scale, Gallery

## Development Notes
- Use TypeScript for new frontend code
- Follow Yii2 conventions for PHP code
- All question types have their own templates in `ko/components/question-form/models/types/`
- Frontend builds are handled by Webpack with multiple entry points
